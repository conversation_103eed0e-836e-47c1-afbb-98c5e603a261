'use client';

import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useChat } from '@/contexts/ChatContext';
import { createMockChatThread, createMockTranscript1, createMockTranscript2 } from './mockData';
import { Transcript } from '@/types';

export default function OmnichannelPage() {
  const router = useRouter();
  const { createThread } = useChat();
  const [isLoading, setIsLoading] = useState(false);

  // Check if we should navigate to journeys after a reload
  useEffect(() => {
    const shouldNavigate = localStorage.getItem('navigateToJourneys');
    if (shouldNavigate === 'true') {
      localStorage.removeItem('navigateToJourneys');
      router.push('/journeys');
    }
  }, [router]);

  const handleLoadData = async () => {
    setIsLoading(true);

    try {
      console.log('Loading mock data...');

      // Create mock chat thread
      const mockThread = createMockChatThread();

      // Create mock transcripts
      const mockTranscript1 = createMockTranscript1();
      const mockTranscript2 = createMockTranscript2();

      // Load existing data from localStorage
      const existingThreads = JSON.parse(localStorage.getItem('chatThreads') || '[]');
      const existingTranscripts = JSON.parse(localStorage.getItem('transcripts') || '[]');

      // Add mock data to existing data (prepend to show as most recent)
      const updatedThreads = [mockThread, ...existingThreads];
      const updatedTranscripts = [mockTranscript2, mockTranscript1, ...existingTranscripts];

      // Save updated data to localStorage
      localStorage.setItem('chatThreads', JSON.stringify(updatedThreads));
      localStorage.setItem('transcripts', JSON.stringify(updatedTranscripts));

      console.log('Mock data loaded successfully!');

      // Set a flag to indicate we should navigate to journeys after reload
      localStorage.setItem('navigateToJourneys', 'true');

      // Reload the page to pick up the new data
      window.location.reload();
    } catch (error) {
      console.error('Error loading mock data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="text-center space-y-6">
        <h1 className="text-2xl font-bold">Omnichannel Conversation</h1>
        <p className="text-muted-foreground">
          Load mock data to simulate a complete omnichannel conversation experience
        </p>
        
        <Button 
          onClick={handleLoadData}
          disabled={isLoading}
          size="lg"
          className="px-8 py-3"
        >
          {isLoading ? 'Loading Data...' : 'Load Data'}
        </Button>
      </div>
    </div>
  );
}
